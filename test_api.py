"""
测试模型配置API
"""
import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

# 测试数据
test_model_data = {
    "name": "deepseek-test",
    "platform": "deepseek",
    "description": "DeepSeek测试模型",
    "api_url": "https://api.deepseek.com",
    "api_key": "***********************************",
    "timeout_seconds": 60,
    "model_name": "deepseek-chat",
    "max_tokens": 1024,
    "prompt": ""
}

def test_model_connection():
    """测试模型连接"""
    url = f"{BASE_URL}/model/test"
    
    print("=== 测试模型连接 ===")
    print(f"URL: {url}")
    print(f"数据: {json.dumps(test_model_data, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(url, json=test_model_data, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 模型连接测试成功！")
            else:
                print("❌ 模型连接测试失败！")
        else:
            print(f"❌ API调用失败，状态码: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {str(e)}")
    except Exception as e:
        print(f"❌ 其他错误: {str(e)}")

if __name__ == "__main__":
    test_model_connection()
