"""
测试 ModelClient 是否正常工作
"""
import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models.model.model_config import ModelConfig
from app.utils.clients.model_client import ModelClient

# 同步测试（对比用）
from openai import OpenAI


async def test_model_client():
    """测试 ModelClient"""

    # 测试配置1：DeepSeek（正确的URL，不带/v1）
    deepseek_config = ModelConfig(
        name="deepseek-test",
        platform="deepseek",
        description="DeepSeek测试配置",
        api_url="https://api.deepseek.com",  # 和你的测试脚本一致
        api_key="***********************************",
        timeout_seconds=60,
        model_name="deepseek-chat",
        max_tokens=1024,
        prompt="",
        status='enabled',
        health_status='unknown'
    )

    # 测试配置2：ModelScope
    modelscope_config = ModelConfig(
        name="modelscope-test",
        platform="local",
        description="ModelScope测试配置",
        api_url="https://api-inference.modelscope.cn/v1",
        api_key="ms-c80847d9-35d3-498e-81a0-2d9175da2c80",
        timeout_seconds=60,
        model_name="ZhipuAI/GLM-4.5",
        max_tokens=1024,
        prompt="",
        status='enabled',
        health_status='unknown'
    )

    # 测试两个配置
    configs = [
        ("DeepSeek", deepseek_config),
        ("ModelScope", modelscope_config)
    ]

    for config_name, test_config in configs:
        print(f"\n{'='*50}")
        print(f"测试 {config_name} 配置")
        print(f"{'='*50}")
        print(f"API URL: {test_config.api_url}")
        print(f"Model Name: {test_config.model_name}")
        print(f"API Key: {test_config.api_key[:10]}...")

        # 创建客户端
        client = ModelClient(test_config)

        try:
            print(f"\n=== {config_name} 健康检查 ===")
            result = await client.health_check()
            print(f"健康检查结果: {result}")

            if result['is_healthy']:
                print(f"\n=== {config_name} 模型调用 ===")
                call_result = await client.call_model("Hello")
                print(f"模型调用结果: {call_result}")
            else:
                print(f"❌ {config_name} 健康检查失败，跳过模型调用测试")

        except Exception as e:
            print(f"❌ {config_name} 测试失败: {str(e)}")
            import traceback
            traceback.print_exc()

        finally:
            await client.close()
            print(f"{config_name} 客户端已关闭")


def test_sync_openai():
    """使用同步OpenAI客户端测试（和你的脚本一样）"""
    print(f"\n{'='*50}")
    print("测试同步OpenAI客户端（对比测试）")
    print(f"{'='*50}")

    try:
        # 完全复制你的测试脚本
        client = OpenAI(
            api_key="***********************************",
            base_url="https://api.deepseek.com"
        )

        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "system", "content": "You are a helpful assistant"},
                {"role": "user", "content": "Hello"},
            ],
            stream=False
        )

        print(f"✅ 同步客户端测试成功!")
        print(f"响应: {response.choices[0].message.content}")

    except Exception as e:
        print(f"❌ 同步客户端测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 先测试同步版本（你的脚本）
    test_sync_openai()

    # 再测试我们的异步版本
    asyncio.run(test_model_client())
